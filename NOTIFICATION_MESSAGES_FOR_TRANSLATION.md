# NAQALAT Notification Messages for Translation

This document contains all notification messages, titles, and user-facing text that need to be translated from English to Arabic for the NAQALAT platform.

## 📋 Overview

The notification system currently sends messages only in English. All these messages need Arabic translations to support bilingual notifications based on user preferences.

## 🔔 Notification Titles (from NotificationService.ts)

These are the notification titles that appear in the notification system:

| Notification Type | English Title | Arabic Translation Needed |
|-------------------|---------------|---------------------------|
| SHIPMENT_CREATED | "📦 Shipment Created" | |
| SHIPMENT_ASSIGNED_QR | "🏷️ QR Code Assigned" | |
| SHIPMENT_DROPPED_OFF | "📤 Package Dropped Off" | |
| SHIPMENT_PICKED_UP | "🚚 Package Picked Up" | |
| SHIPMENT_IN_TRANSIT | "🚛 Package In Transit" | |
| SHIPMENT_ARRIVED | "📍 Package Arrived" | |
| SHIPMENT_READY_FOR_DELIVERY | "✅ Ready for Pickup" | |
| SHIPMENT_DELIVERED | "🎉 Package Delivered" | |
| SHIPMENT_CANCELLED | "❌ Shipment Cancelled" | |
| SHIPMENT_EXPIRED | "⏰ Shipment Expired" | |
| SHIPMENT_STATUS_CHANGED | "🔄 Status Updated" | |
| QR_CODE_ASSIGNED | "🏷️ QR Code Ready" | |
| PACKAGE_READY_FOR_PICKUP | "📦 Package Ready" | |
| DELIVERY_REMINDER | "⏰ Delivery Reminder" | |
| SYSTEM_ALERT | "🚨 System Alert" | |
| USER_REGISTERED | "👤 User Registered" | |
| USER_EMAIL_VERIFIED | "📧 User Email Verified" | |
| OPERATOR_NEEDS_APPROVAL | "📋 Operator Needs Approval" | |
| USER_STATUS_CHANGED | "🔄 User Status Changed" | |
| SECURITY_ALERT | "🔒 Security Alert" | |
| SYSTEM_ERROR | "💥 System Error" | |
| ADMIN_CREATED | "👑 Admin Created" | |
| BULK_OPERATION_COMPLETED | "📊 Bulk Operation Completed" | |
| SYSTEM_MAINTENANCE | "🛠️ System Maintenance" | |
| Default fallback | "📢 Notification" | |

## 📦 Shipment Notification Messages (from ShipmentNotificationService.ts)

### QR Code Assignment Messages

**Customer Messages:**
- Title: "🏷️ QR Code Assigned"
- Message: "Your package has been accepted at {business_name}. QR code assigned and package is now in the system."

**Origin Access Operator Messages:**
- Title: "📦 Package Received & QR Assigned"
- Message: "Package from {customer_name} has been received and QR code assigned. Ready for car operator pickup."

**Destination Access Operator Messages:**
- Title: "📦 Package in System"
- Message: "Package for {receiver_name} is now in the system and will be transported to your location."

**Car Operator Messages:**
- Title: "📦 Package Ready for Pickup"
- Message: "Package is ready for pickup at {origin_business_name}. Destination: {dest_business_name}"

### Drop-off Scan Messages

**Customer Messages:**
- Title: "📤 Package Dropped Off"
- Message: "Your package has been successfully dropped off at {business_name}. It's now awaiting pickup by a car operator."

**Access Operator Messages:**
- Title: "📦 Package Received"
- Message: "Package from {customer_name} has been received and stored. Ready for car operator pickup."

**Car Operator Messages:**
- Title: "📦 Package Available for Pickup"
- Message: "Package is now available for pickup at {origin_business_name}. Destination: {dest_business_name}"

### Pickup Scan Messages

**Customer Messages:**
- Title: "🚚 Package Picked Up"
- Message: "Your package has been picked up by a car operator from {origin_business_name} and is now in transit to {dest_business_name}."

**Origin Access Operator Messages:**
- Title: "🚚 Package Collected"
- Message: "Package has been collected by car operator {car_operator_name}. Package is now in transit."

**Car Operator Messages:**
- Title: "📦 Pickup Confirmed"
- Message: "Package pickup confirmed from {origin_business_name}. Destination: {dest_business_name}"

**Destination Access Operator Messages:**
- Title: "🚛 Package In Transit"
- Message: "Package for {receiver_name} is now in transit from {origin_business_name}. Prepare for arrival."

### Arrival Scan Messages

**Customer Messages:**
- Title: "📍 Package Arrived"
- Message: "Your package has arrived at {dest_business_name}. The receiver {receiver_name} can now collect it."

**Car Operator Messages:**
- Title: "✅ Delivery Confirmed"
- Message: "Package delivery to {dest_business_name} has been confirmed. Transport completed successfully."

**Destination Access Operator Messages:**
- Title: "📦 Package Ready for Delivery"
- Message: "Package for {receiver_name} has been received and is ready for customer pickup."

### Final Delivery Messages

**Customer Messages:**
- Title: "🎉 Package Delivered"
- Message: "Your package has been successfully delivered to {receiver_name}. Thank you for using our service!"

**Destination Access Operator Messages:**
- Title: "✅ Delivery Completed"
- Message: "Package for {receiver_name} has been successfully delivered. Shipment completed."

**Origin Access Operator Messages:**
- Title: "🎉 Shipment Journey Completed"
- Message: "Package from your location has been successfully delivered to {receiver_name} at {dest_business_name}."

**Car Operator Messages:**
- Title: "🚚 Transport Completed"
- Message: "Package you transported has been successfully delivered to {receiver_name}. Great job!"

### Shipment Creation Messages

**Customer Messages:**
- Title: "📦 Shipment Created Successfully"
- Message: "Your shipment has been created! You have 24 hours to drop it off at {origin_business_name}. Pickup code: {pickup_code}"

**Origin Access Operator Messages:**
- Title: "📦 New Shipment Incoming"
- Message: "Customer {customer_name} created a shipment for drop-off at your location. Expected within 24 hours."

**Destination Access Operator Messages:**
- Title: "📦 Future Delivery Scheduled"
- Message: "A shipment has been created for delivery to your location. Receiver: {receiver_name}"

**Car Operator Messages:**
- Title: "🚚 New Transport Opportunity"
- Message: "New shipment available for transport from {origin_business_name} to {dest_business_name}"

## 🔄 Status Change Messages (Dynamic)

These messages are generated based on shipment status and user type:

### Status Change Titles
- "🎉 Package Delivered"
- "✅ Ready for Pickup"
- "📍 Package Arrived"
- "🚛 In Transit"
- "🚚 Package Picked Up"
- "📦 Awaiting Pickup"
- "🔄 Status Updated" (default)

### Cancellation Messages
- "by customer request"
- "due to expiration"
- "by administrator"
- "for unknown reason"

### Status-specific Messages by User Type

**DELIVERED Status:**
- Customer: "Your package has been delivered to {receiver_name}!"
- Others: "Package {tracking_code} has been successfully delivered."

**READY_FOR_DELIVERY Status:**
- Customer: "Your package is ready for pickup at {dest_business_name}."
- Others: "Package {tracking_code} is ready for customer pickup."

**ARRIVED_AT_DESTINATION Status:**
- Customer: "Your package has arrived at {dest_business_name}."
- Others: "Package {tracking_code} has arrived at destination."

**IN_TRANSIT Status:**
- Customer: "Your package is now in transit to {dest_business_name}."
- Others: "Package {tracking_code} is now in transit."

**PICKED_UP_BY_CO Status:**
- Customer: "Your package has been picked up for transport."
- Others: "Package {tracking_code} has been picked up by car operator."

**AWAITING_PICKUP Status:**
- Customer: "Your package has been received and is awaiting pickup."
- Others: "Package {tracking_code} is ready for car operator pickup."

## 📧 Email Template Messages

The system also has email templates that need translation. These are found in:
- `src/utils/emailTemplates.ts` (English templates)
- `src/utils/arabicEmailTemplates.ts` (Arabic templates - already exist)
- `src/utils/bilingualEmailTemplates.ts` (Bilingual templates)

## 🚨 Error Messages

Common error messages that appear in API responses:

### Authentication Errors
- "Authentication required"
- "Invalid token payload"
- "Access denied. {USER_TYPE} access required."

### Validation Errors
- "Validation error"
- "Missing required fields: {field_names}"
- "Invalid {field_name}"

### File Upload Errors
- "File too large"
- "Photo must be less than 5MB. Please compress your image and try again."
- "Request too large"

### General Errors
- "Internal server error"
- "Failed to retrieve {resource}. Please try again later."
- "Invalid pagination parameters"
- "Invalid search parameters"
- "The search query contains invalid parameters. Please check your search terms and try again."

## ✅ Success Messages

Common success messages in API responses:

### User Registration
- "User registered successfully. Please check your email to verify your account."
- "After verification, your account will require admin approval before you can use the platform."
- "After verification, you can immediately start using the platform."

### CRUD Operations
- "{Resource} created successfully"
- "{Resource} updated successfully"
- "{Resource} retrieved successfully"
- "{Resource} deleted successfully"

### Dashboard Messages
- "Dashboard data retrieved successfully"
- "System overview retrieved successfully"

## 🔧 Implementation Notes

1. **Current State**: All notification messages are hardcoded in English
2. **User Preferences**: Users have language preferences stored in `NotificationPreference.preferred_language`
3. **Email Format**: Users can choose between 'ENGLISH', 'ARABIC', or 'BILINGUAL' email formats
4. **Translation Needed**: All the above messages need Arabic translations
5. **Dynamic Content**: Messages contain placeholders like `{business_name}`, `{customer_name}`, etc.

## 📝 Next Steps for Frontend Development

1. **Create Translation Files**: Create JSON/TypeScript files with all message translations
2. **Implement i18n**: Set up internationalization system for the frontend
3. **API Integration**: Ensure frontend can handle both English and Arabic notifications
4. **User Preferences**: Implement UI for users to set their preferred language
5. **Testing**: Test all notification scenarios in both languages

## 📱 API Response Messages

### Admin Controller Messages
- "System overview retrieved successfully"
- "Notification templates retrieved successfully"
- "Notification broadcasted successfully"
- "Template created successfully"
- "User registered successfully. Please check your email to verify your account."
- "After verification, your account will require admin approval before you can use the platform."
- "After verification, you can immediately start using the platform."

### Shipment Controller Messages
- "Failed to retrieve pending shipments. Please try again later."
- "Failed to retrieve shipments. Please try again later."
- "Invalid search parameters"
- "The search query contains invalid parameters. Please check your search terms and try again."

### Access Operator Controller Messages
- "Access point created successfully"
- "Access point updated successfully"
- "Failed to retrieve access operators. Please try again later."
- "Invalid pagination parameters"

### Dashboard Controller Messages
- "Dashboard data retrieved successfully"
- "Invalid token payload"
- "User not found"

## 🔍 Validation Schema Messages

These are validation error messages that come from Zod schemas:

### Common Validation Messages
- "Required" (for missing fields)
- "Invalid email format"
- "Password must be at least 8 characters"
- "Invalid UUID format"
- "String must contain at least 1 character(s)"
- "String must contain at most 255 character(s)"
- "Number must be greater than 0"
- "Invalid enum value"

### Specific Field Validations
- "Invalid phone number format"
- "Invalid coordinates"
- "File size too large"
- "Invalid file type"
- "Invalid date format"

## � Console Log Messages

These appear in server logs but may be visible in development:

### Success Logs
- "✅ Sent {count} QR assignment notifications"
- "✅ Sent {count} dropoff scan notifications"
- "✅ Sent {count} pickup scan notifications"
- "✅ Sent {count} arrival scan notifications"
- "✅ Sent {count} delivery notifications"
- "✅ Sent {count} shipment creation notifications"
- "✅ Sent {count} status change notifications"
- "✅ Sent delivery completion emails"
- "✅ Sent shipment creation emails"
- "✅ Created {count} bulk notifications"

### Error Logs
- "❌ Failed to send QR assignment notifications:"
- "❌ Failed to send dropoff scan notifications:"
- "❌ Failed to send pickup scan notifications:"
- "❌ Failed to send arrival scan notifications:"
- "❌ Failed to send delivery notifications:"
- "❌ Failed to send shipment creation notifications:"
- "❌ Failed to send status change notifications:"
- "❌ Failed to send delivery completion emails:"
- "❌ Failed to send shipment creation emails:"
- "❌ Failed to get eligible car operators:"
- "❌ Failed to get shipment context:"

### Info Logs
- "⚠️ No active admins found to notify"
- "📧 Creating notification for user {userId}"
- "📧 Creating {count} bulk notifications"

## �🎯 Priority Messages

**High Priority** (Most frequently seen by users):
- Shipment creation, pickup, delivery notifications
- Status change messages
- Authentication and validation errors
- API response success/error messages

**Medium Priority**:
- Admin notifications
- System alerts
- Success messages
- Validation error messages

**Low Priority**:
- System maintenance messages
- Bulk operation notifications
- Console log messages

## 🔧 Technical Implementation Requirements

### For Backend Developers:
1. **Notification Service Enhancement**: Modify `NotificationService.ts` to support bilingual titles
2. **Message Templates**: Create message template system with language support
3. **User Preference Integration**: Use `NotificationPreference.preferred_language` to determine message language
4. **API Response Localization**: Implement localization for all API response messages

### For Frontend Developers:
1. **Translation Files**: Create comprehensive translation files for all messages
2. **Notification Display**: Implement proper display of bilingual notifications
3. **Language Switching**: Allow users to change notification language preferences
4. **Error Handling**: Display localized error messages from API responses
5. **Real-time Updates**: Ensure WebSocket/SSE notifications support both languages

### Database Considerations:
1. **Notification Storage**: Consider storing notifications in both languages
2. **User Preferences**: Ensure `preferred_language` field is properly utilized
3. **Template Storage**: May need notification template storage for dynamic content

## 📋 Translation Checklist

- [ ] Notification titles (25 types)
- [ ] Shipment notification messages (20+ variations)
- [ ] Status change messages (6 statuses × 3 user types)
- [ ] Error messages (10+ common errors)
- [ ] Success messages (15+ API responses)
- [ ] Validation messages (20+ field validations)
- [ ] Email template integration
- [ ] Console log messages (optional)
- [ ] API documentation updates
- [ ] Frontend implementation
- [ ] Testing in both languages
